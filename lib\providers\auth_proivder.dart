import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_service.dart';
import '../services/auth_service.dart';

/// حالة المصادقة
abstract class AuthState {
  const AuthState();
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final Map<String, dynamic> user;
  final int? role;

  const AuthAuthenticated({required this.user, this.role});
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthError extends AuthState {
  final String message;
  const AuthError(this.message);
}

/// مزود المصادقة
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthInitial()) {
    _initAuth();
  }

  /// تهيئة المصادقة
  Future<void> _initAuth() async {
    state = const AuthLoading();

    try {
      final user = SupabaseService.client.auth.currentUser;
      if (user != null) {
        await _loadUserData();
      } else {
        state = const AuthUnauthenticated();
      }
    } catch (e) {
      state = AuthError('خطأ في تهيئة المصادقة: $e');
    }
  }

  /// تحميل بيانات المستخدم
  Future<void> _loadUserData() async {
    try {
      final user = SupabaseService.client.auth.currentUser;
      if (user == null) {
        state = const AuthUnauthenticated();
        return;
      }

      // Get user profile from users table
      final userProfile = await SupabaseService.client
          .from('users')
          .select()
          .eq('id', user.id)
          .maybeSingle();

      if (userProfile == null) {
        state = const AuthUnauthenticated();
        return;
      }

      final int? role = await _authService.getCurrentUserRole();

      state = AuthAuthenticated(user: userProfile, role: role);
    } catch (e) {
      state = AuthError('خطأ في تحميل بيانات المستخدم: $e');
    }
  }

  /// تسجيل الدخول
  Future<bool> signIn({required String email, required String password}) async {
    state = const AuthLoading();

    try {
      final response = await _authService.signIn(email: email, password: password);

      if (response != null && response.user != null) {
        await _loadUserData();
        return true;
      }

      state = const AuthUnauthenticated();
      return false;
    } catch (e) {
      state = AuthError('خطأ في تسجيل الدخول: $e');
      return false;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _authService.signOut();
      state = const AuthUnauthenticated();
    } catch (e) {
      state = AuthError('خطأ في تسجيل الخروج: $e');
    }
  }

  /// تحديث بيانات المستخدم
  Future<void> updateUserData() async {
    if (state is AuthAuthenticated) {
      await _loadUserData();
    }
  }
}

/// مزود المصادقة الرئيسي
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = AuthService();
  return AuthNotifier(authService);
});
