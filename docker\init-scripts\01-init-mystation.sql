-- Initialization script for MyStation Electricity Management System
-- This script sets up the basic database structure

-- Create custom schemas
CREATE SCHEMA IF NOT EXISTS electricity;
CREATE SCHEMA IF NOT EXISTS maintenance;
CREATE SCHEMA IF NOT EXISTS reports;

-- Set up permissions
GRANT USAGE ON SCHEMA electricity TO postgres, anon, authenticated, service_role;
GRANT USAGE ON SCHEMA maintenance TO postgres, anon, authenticated, service_role;
GRANT USAGE ON SCHEMA reports TO postgres, anon, authenticated, service_role;

-- Enable Row Level Security by default
ALTER DEFAULT PRIVILEGES IN SCHEMA electricity GRANT ALL ON TABLES TO postgres, anon, authenticated, service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA maintenance GRANT ALL ON TABLES TO postgres, anon, authenticated, service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA reports GRANT ALL ON TABLES TO postgres, anon, authenticated, service_role;

-- Create some basic tables for electricity management
CREATE TABLE IF NOT EXISTS electricity.stations (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
    location VARCHAR(255),
    capacity_mw DECIMAL(10,2),
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS electricity.meters (
    id SERIAL PRIMARY KEY,
    station_id INTEGER REFERENCES electricity.stations(id),
    meter_number VARCHAR(100) UNIQUE NOT NULL,
    meter_type VARCHAR(50),
    installation_date DATE,
    last_reading DECIMAL(15,2),
    last_reading_date TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS electricity.readings (
    id SERIAL PRIMARY KEY,
    meter_id INTEGER REFERENCES electricity.meters(id),
    reading_value DECIMAL(15,2) NOT NULL,
    reading_date TIMESTAMP WITH TIME ZONE NOT NULL,
    reader_id UUID REFERENCES auth.users(id),
    notes TEXT,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS maintenance.schedules (
    id SERIAL PRIMARY KEY,
    station_id INTEGER REFERENCES electricity.stations(id),
    maintenance_type VARCHAR(100) NOT NULL,
    scheduled_date DATE NOT NULL,
    assigned_to UUID REFERENCES auth.users(id),
    status VARCHAR(50) DEFAULT 'scheduled',
    priority VARCHAR(20) DEFAULT 'medium',
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS maintenance.logs (
    id SERIAL PRIMARY KEY,
    schedule_id INTEGER REFERENCES maintenance.schedules(id),
    performed_by UUID REFERENCES auth.users(id),
    performed_date TIMESTAMP WITH TIME ZONE NOT NULL,
    work_description TEXT NOT NULL,
    parts_used TEXT,
    cost DECIMAL(10,2),
    photos TEXT[], -- Array of photo URLs
    status VARCHAR(50) DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_stations_status ON electricity.stations(status);
CREATE INDEX IF NOT EXISTS idx_meters_station_id ON electricity.meters(station_id);
CREATE INDEX IF NOT EXISTS idx_meters_status ON electricity.meters(status);
CREATE INDEX IF NOT EXISTS idx_readings_meter_id ON electricity.readings(meter_id);
CREATE INDEX IF NOT EXISTS idx_readings_date ON electricity.readings(reading_date);
CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_station_id ON maintenance.schedules(station_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_date ON maintenance.schedules(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_schedule_id ON maintenance.logs(schedule_id);

-- Enable Row Level Security
ALTER TABLE electricity.stations ENABLE ROW LEVEL SECURITY;
ALTER TABLE electricity.meters ENABLE ROW LEVEL SECURITY;
ALTER TABLE electricity.readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance.schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance.logs ENABLE ROW LEVEL SECURITY;

-- Create basic RLS policies (you can customize these based on your needs)
CREATE POLICY "Users can view all stations" ON electricity.stations FOR SELECT USING (true);
CREATE POLICY "Users can view all meters" ON electricity.meters FOR SELECT USING (true);
CREATE POLICY "Users can view all readings" ON electricity.readings FOR SELECT USING (true);
CREATE POLICY "Users can view all maintenance schedules" ON maintenance.schedules FOR SELECT USING (true);
CREATE POLICY "Users can view all maintenance logs" ON maintenance.logs FOR SELECT USING (true);

-- Insert some sample data
INSERT INTO electricity.stations (name, location, capacity_mw, status) VALUES
('Main Station', 'Downtown', 100.50, 'active'),
('North Station', 'North District', 75.25, 'active'),
('South Station', 'South District', 50.00, 'maintenance')
ON CONFLICT DO NOTHING;

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_stations_updated_at BEFORE UPDATE ON electricity.stations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meters_updated_at BEFORE UPDATE ON electricity.meters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_schedules_updated_at BEFORE UPDATE ON maintenance.schedules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
