# GoTrue Auth Dockerfile for MyStation
# Based on Supabase GoTrue authentication service

FROM public.ecr.aws/supabase/gotrue:v2.178.0

# Set environment variables
ENV GOTRUE_API_HOST=0.0.0.0
ENV GOTRUE_API_PORT=9999
ENV GOTRUE_DB_DRIVER=postgres
ENV GOTRUE_LOG_LEVEL=info
ENV GOTRUE_RATE_LIMIT_HEADER=X-Real-IP
ENV GOTRUE_RATE_LIMIT_EMAIL_SENT=60

# Create necessary directories (if needed)
# USER root
# RUN mkdir -p /app/config
# USER gotrue

# Copy custom configuration if needed
# COPY auth-config.json /app/config/

# Expose GoTrue port
EXPOSE 9999

# Custom healthcheck
HEALTHCHECK --interval=10s --timeout=5s --retries=5 --start-period=30s \
    CMD curl -f http://localhost:9999/health || exit 1

# Default command
CMD ["auth"]
