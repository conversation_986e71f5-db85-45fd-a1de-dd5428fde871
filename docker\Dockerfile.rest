# PostgREST API Dockerfile for MyStation
# Based on Supabase PostgREST service

FROM public.ecr.aws/supabase/postgrest:v13.0.4

# Set environment variables
ENV PGRST_DB_SCHEMAS=public,storage,graphql_public
ENV PGRST_DB_ANON_ROLE=anon
ENV PGRST_DB_USE_LEGACY_GUCS=false

# Create necessary directories (if needed)
# USER root
# RUN mkdir -p /app/config
# USER postgrest

# Copy custom configuration if needed
# COPY postgrest.conf /app/config/

# Expose PostgREST port
EXPOSE 3000

# Custom healthcheck
HEALTHCHECK --interval=10s --timeout=5s --retries=5 --start-period=30s \
    CMD curl -f http://localhost:3000/ || exit 1

# Default command
CMD ["postgrest"]
