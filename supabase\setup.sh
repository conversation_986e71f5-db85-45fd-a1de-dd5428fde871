#!/bin/bash

# Electricity Management System - Supabase Setup Script
# This script helps set up the database schema, seed data, and storage configuration

echo "🚀 Setting up Electricity Management System..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "config.toml" ]; then
    echo "❌ Please run this script from the supabase directory"
    exit 1
fi

echo "📋 Current Supabase status:"
supabase status

echo ""
echo "🔄 Applying database migrations..."

# Apply migrations
supabase db reset --linked

if [ $? -eq 0 ]; then
    echo "✅ Database setup completed successfully!"
    echo ""
    echo "📊 Your electricity management system now includes:"
    echo "   • 6 core tables (profiles, stations, meters, readings, billing, maintenance)"
    echo "   • 4 storage buckets (documents, meter_images, reports, maintenance_photos)"
    echo "   • Sample data (3 stations, 15 meters, sample readings and billing)"
    echo "   • Row Level Security policies for data protection"
    echo "   • User role management (admin, manager, operator, viewer)"
    echo ""
    echo "🔐 Next steps:"
    echo "   1. Sign up through your Flutter app"
    echo "   2. Update your user role to 'admin' in the database:"
    echo "      UPDATE public.profiles SET role = 'admin' WHERE email = '<EMAIL>';"
    echo "   3. Start using the system!"
    echo ""
    echo "📖 For more information, see README.md"
else
    echo "❌ Database setup failed. Please check the error messages above."
    echo "💡 Common issues:"
    echo "   • Make sure Supabase is running: supabase start"
    echo "   • Check if you're linked to a remote project: supabase link"
    echo "   • Verify your database connection"
    exit 1
fi
