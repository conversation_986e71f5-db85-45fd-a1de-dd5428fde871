// lib/services/local_database_service.dart
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';

class LocalDatabaseService {
  static Database? _database;
  static const int _databaseVersion = 1;
  
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }
  
  Future<void> initialize() async {
    await database;
    debugPrint('🗄️ Local database initialized');
  }
  
  Future<Database> _initDatabase() async {
    try {
      String path = join(await getDatabasesPath(), 'mystation.db');
      debugPrint('🗄️ Initializing local database at: $path');
      
      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
      );
    } catch (e) {
      debugPrint('❌ Error initializing database: $e');
      rethrow;
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    debugPrint('🔄 Creating local database tables...');
    
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        full_name TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        phone TEXT,
        created_at TEXT,
        updated_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE stations (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        location TEXT,
        created_at TEXT,
        updated_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE roles (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE station_users (
        id INTEGER PRIMARY KEY,
        user_id TEXT,
        station_id INTEGER,
        role_id INTEGER,
        assigned_at TEXT,
        sync_status TEXT DEFAULT 'pending'
      )
    ''');

    await db.execute('''
      CREATE TABLE station_subscription_requests (
        id INTEGER PRIMARY KEY,
        user_id TEXT NOT NULL,
        station_name TEXT NOT NULL,
        manager_name TEXT NOT NULL,
        manager_email TEXT NOT NULL,
        manager_phone TEXT,
        requested_plan_id INTEGER NOT NULL,
        transfer_code TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        station_address TEXT,
        created_at TEXT,
        updated_at TEXT,
        sync_status TEXT DEFAULT 'pending'
      )
    ''');

    debugPrint('✅ Database tables created successfully');
  }
}
