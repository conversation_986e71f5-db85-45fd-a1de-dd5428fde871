import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'supabase_service.dart';

class AuthService {
  Future<AuthResponse?> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await SupabaseService.client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      debugPrint('خطأ في تسجيل الدخول: $e');
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      await SupabaseService.client.auth.signOut();
    } catch (e) {
      debugPrint('خطأ في تسجيل الخروج: $e');
      rethrow;
    }
  }

  Future<int?> getCurrentUserRole() async {
    try {
      final user = SupabaseService.client.auth.currentUser;
      if (user == null) return null;

      final response = await SupabaseService.client
          .from('station_users')
          .select('role_id')
          .eq('user_id', user.id)
          .maybeSingle();

      return response?['role_id'];
    } catch (e) {
      debugPrint('خطأ في الحصول على دور المستخدم: $e');
      return null;
    }
  }

  Future<bool> hasRole(int roleId) async {
    try {
      final userRole = await getCurrentUserRole();
      return userRole == roleId;
    } catch (e) {
      debugPrint('خطأ في فحص الدور: $e');
      return false;
    }
  }

  Future<int?> getCurrentUserStationId() async {
    try {
      final user = SupabaseService.client.auth.currentUser;
      if (user == null) return null;

      final response = await SupabaseService.client
          .from('station_users')
          .select('station_id')
          .eq('user_id', user.id)
          .maybeSingle();

      return response?['station_id'];
    } catch (e) {
      debugPrint('خطأ في الحصول على معرف المحطة: $e');
      return null;
    }
  }
}
