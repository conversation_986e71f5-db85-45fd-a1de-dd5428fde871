# Kong API Gateway Dockerfile for MyStation
# Based on Supabase Kong configuration

FROM public.ecr.aws/supabase/kong:2.8.1

# Set environment variables
ENV KONG_DATABASE=off
ENV KONG_DECLARATIVE_CONFIG=/var/lib/kong/kong.yml
ENV KONG_DNS_ORDER=LAST,A,CNAME
ENV KONG_PLUGINS=request-transformer,cors,key-auth,acl
ENV KONG_WORKER_PROCESSES=2
ENV KONG_WORKER_CONNECTIONS=1024
ENV KONG_LOG_LEVEL=info

# Copy custom Kong configuration
COPY kong.yml /var/lib/kong/kong.yml

# Expose Kong ports
EXPOSE 8000 8001

# Custom healthcheck
HEALTHCHECK --interval=10s --timeout=5s --retries=5 --start-period=30s \
    CMD kong health

# Default command
CMD ["kong", "start", "--vv"]
