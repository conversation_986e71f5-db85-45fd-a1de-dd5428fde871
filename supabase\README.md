# Electricity Management System - Supabase Configuration

This directory contains the Supabase configuration for the Electricity Management System, including database schema, authentication, and storage setup.

## 🗄️ Database Schema

### Core Tables

#### 1. **profiles**
- Extends Supabase `auth.users` with additional user information
- **Fields**: id, email, full_name, phone, role, station_id, is_active, created_at, updated_at
- **Roles**: admin, manager, operator, viewer

#### 2. **stations**
- Power stations managed by the system
- **Fields**: id, name, code, location, address, capacity_kw, voltage_level, status, manager_id
- **Status**: active, inactive, maintenance

#### 3. **meters**
- Electricity meters connected to stations
- **Fields**: id, meter_number, station_id, customer_name, customer_phone, customer_address, meter_type, status, last_reading, multiplier
- **Status**: active, inactive, maintenance, faulty

#### 4. **meter_readings**
- Individual meter readings with consumption data
- **Fields**: id, meter_id, reading_value, reading_date, consumption, status, notes, read_by
- **Status**: valid, suspicious, error, estimated

#### 5. **billing**
- Billing records for electricity consumption
- **Fields**: id, meter_id, billing_period_start, billing_period_end, consumption_kwh, rate_per_kwh, total_amount, status, due_date, paid_date, payment_method, invoice_number
- **Status**: pending, paid, overdue, cancelled

#### 6. **maintenance_logs**
- Maintenance activities for stations and meters
- **Fields**: id, station_id, meter_id, maintenance_type, description, performed_by, maintenance_date, next_maintenance_date, cost, status

## 🔐 Authentication & Authorization

### User Roles & Permissions

- **Admin**: Full access to all data and functions
- **Manager**: Can manage stations, meters, billing, and view all data in their stations
- **Operator**: Can create meter readings and maintenance logs
- **Viewer**: Read-only access to assigned stations

### Row Level Security (RLS)

All tables have RLS enabled with policies that:
- Restrict access based on user role and station ownership
- Allow users to view data for stations they manage
- Control write operations based on user permissions

### Automatic Profile Creation

When a user signs up through Supabase Auth:
1. A profile is automatically created in the `profiles` table
2. Default role is set to 'viewer'
3. Profile can be updated by the user or administrators

## 📁 Storage Configuration

### Storage Buckets

#### 1. **documents** (10MB limit)
- **Purpose**: General documents, contracts, specifications
- **Access**: Managers and admins can upload/delete, users can view their station's documents
- **File Types**: PDF, Word documents, text files

#### 2. **meter_images** (5MB limit)
- **Purpose**: Photos of meters, installations, damage reports
- **Access**: Operators and above can upload, managers and admins can delete
- **File Types**: JPEG, PNG, GIF

#### 3. **reports** (25MB limit)
- **Purpose**: Generated reports, analytics, exports
- **Access**: Managers and admins have full control
- **File Types**: PDF, Excel files

#### 4. **maintenance_photos** (10MB limit)
- **Purpose**: Photos from maintenance activities
- **Access**: Operators and above can upload, managers and admins can delete
- **File Types**: JPEG, PNG

### Storage Organization

Files are organized by station code and meter ID:
```
bucket_name/
├── station_code/
│   ├── meter_id/
│   │   └── filename.ext
│   └── general/
│       └── filename.ext
```

## 🚀 Getting Started

### 1. Apply Migrations

```bash
# Apply the database schema
supabase db reset

# Or apply specific migrations
supabase db push
```

### 2. Seed Data

The system comes with sample data including:
- 3 sample stations (Central, Industrial, Residential)
- 15 sample meters (5 per station)
- Sample readings, billing, and maintenance records

### 3. Create Admin User

1. Sign up through your Flutter app or Supabase Auth
2. Connect to the database and update the user's role:

```sql
UPDATE public.profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

### 4. Configure Storage Buckets

Storage buckets are automatically created when you run the migrations. You can customize the configuration in `config.toml`.

## 🔧 Database Functions

### Utility Functions

- **`get_user_stations(user_uuid)`**: Returns stations accessible to a user
- **`get_meter_consumption_summary(meter_uuid, start_date, end_date)`**: Returns consumption statistics
- **`get_storage_usage_by_station(station_code)`**: Returns storage usage per station
- **`cleanup_orphaned_storage()`**: Cleans up orphaned storage objects

### Triggers

- **`update_updated_at_column()`**: Automatically updates `updated_at` timestamps
- **`handle_new_user()`**: Creates profile when user signs up

## 📊 Sample Queries

### Get Station Summary
```sql
SELECT 
    s.name,
    s.code,
    COUNT(m.id) as meter_count,
    SUM(m.last_reading) as total_consumption
FROM public.stations s
LEFT JOIN public.meters m ON s.id = m.station_id
GROUP BY s.id, s.name, s.code;
```

### Get User's Accessible Data
```sql
SELECT * FROM public.get_user_stations(auth.uid());
```

### Get Meter Consumption
```sql
SELECT * FROM public.get_meter_consumption_summary(
    'meter-uuid-here',
    CURRENT_DATE - INTERVAL '1 month',
    CURRENT_DATE
);
```

## 🛡️ Security Features

- **Row Level Security**: Data access controlled by user role and station ownership
- **Input Validation**: Database constraints and triggers ensure data integrity
- **Audit Trail**: All tables include created_at/updated_at timestamps
- **Secure Storage**: Storage access controlled by policies

## 📝 Notes

- The system uses UUIDs for all primary keys
- Timestamps are stored with timezone information
- All monetary values use DECIMAL for precision
- Consumption values are stored in kWh
- The system supports multiple voltage levels and meter types

## 🔄 Maintenance

### Regular Tasks
- Monitor storage usage per station
- Clean up orphaned storage objects
- Review and update user roles as needed
- Backup database and storage regularly

### Performance Optimization
- Indexes are created on frequently queried columns
- Consider partitioning large tables by date if needed
- Monitor query performance and add indexes as needed

## 📞 Support

For issues or questions about the database configuration:
1. Check the Supabase logs: `supabase logs`
2. Review the migration files for syntax errors
3. Ensure all extensions are properly installed
4. Verify RLS policies are working correctly
