# Use the official Supabase PostgreSQL image as base
FROM public.ecr.aws/supabase/postgres:**********

# Set environment variables
ENV POSTGRES_USER=postgres
ENV POSTGRES_PASSWORD=postgres
ENV POSTGRES_DB=postgres

# Copy custom initialization scripts if needed
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Expose PostgreSQL port
EXPOSE 5432

# Add any custom configurations
COPY postgresql.conf /etc/postgresql/postgresql.conf

# Set the default command
CMD ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]
