// lib/core/utils/network_utils.dart
import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

class NetworkUtils {
  // فحص الاتصال بالإنترنت
  static Future<bool> isConnected() async {
    final List<ConnectivityResult> connectivityResults = await Connectivity().checkConnectivity();
    return connectivityResults.isNotEmpty && connectivityResults.first != ConnectivityResult.none;
  }
  
  // فحص نوع الاتصال
  static Future<String> getConnectionType() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    
    switch (connectivityResult) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.other:
        return 'Other';
      default:
        return 'None';
    }
  }
  
  // فحص جودة الاتصال
  static Future<String> getConnectionQuality() async {
    try {
      // يمكن إضافة منطق فحص جودة الاتصال هنا
      // مثل ping أو speed test
      return 'Good';
    } catch (e) {
      return 'Unknown';
    }
  }
  
  // فحص الاتصال بخادم معين
  static Future<bool> isServerReachable(String url) async {
    try {
      // يمكن إضافة منطق فحص الاتصال بالخادم هنا
      return true;
    } catch (e) {
      return false;
    }
  }
  
  // الحصول على معلومات الاتصال
  static Future<Map<String, dynamic>> getConnectionInfo() async {
    final isConnected = await NetworkUtils.isConnected();
    final connectionType = await NetworkUtils.getConnectionType();
    final connectionQuality = await NetworkUtils.getConnectionQuality();
    
    return {
      'isConnected': isConnected,
      'connectionType': connectionType,
      'connectionQuality': connectionQuality,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  // مراقبة تغييرات الاتصال - إصلاح نوع البيانات
  static Stream<ConnectivityResult> getConnectionStream() {
    return Connectivity().onConnectivityChanged.map((results) {
      // إذا كان هناك أكثر من نوع اتصال، نأخذ الأول
      if (results.isNotEmpty) {
        return results.first;
      }
      return ConnectivityResult.none;
    });
  }
  
  // بديل: مراقبة تغييرات الاتصال مع إرجاع List
  static Stream<List<ConnectivityResult>> getConnectionListStream() {
    return Connectivity().onConnectivityChanged;
  }
  
  // بديل: مراقبة تغييرات الاتصال مع إرجاع bool
  static Stream<bool> getConnectionStatusStream() {
    return Connectivity().onConnectivityChanged.map((results) {
      return results.isNotEmpty && results.first != ConnectivityResult.none;
    });
  }
  
  // انتظار الاتصال
  static Future<void> waitForConnection() async {
    while (!await isConnected()) {
      await Future.delayed(const Duration(seconds: 1));
    }
  }
  
  // انتظار الاتصال مع timeout
  static Future<bool> waitForConnectionWithTimeout(Duration timeout) async {
    final startTime = DateTime.now();
    
    while (!await isConnected()) {
      if (DateTime.now().difference(startTime) > timeout) {
        return false;
      }
      await Future.delayed(const Duration(seconds: 1));
    }
    
    return true;
  }
  
  // الحصول على أفضل نوع اتصال متاح
  static Future<ConnectivityResult> getBestConnectionType() async {
    final results = await Connectivity().checkConnectivity();
    
    // ترتيب الأولوية: Ethernet > WiFi > Mobile > VPN > Bluetooth > Other
    if (results.contains(ConnectivityResult.ethernet)) {
      return ConnectivityResult.ethernet;
    } else if (results.contains(ConnectivityResult.wifi)) {
      return ConnectivityResult.wifi;
    } else if (results.contains(ConnectivityResult.mobile)) {
      return ConnectivityResult.mobile;
    } else if (results.contains(ConnectivityResult.vpn)) {
      return ConnectivityResult.vpn;
    } else if (results.contains(ConnectivityResult.bluetooth)) {
      return ConnectivityResult.bluetooth;
    } else if (results.contains(ConnectivityResult.other)) {
      return ConnectivityResult.other;
    } else {
      return ConnectivityResult.none;
    }
  }
  
  // فحص إذا كان الاتصال مستقر
  static Future<bool> isConnectionStable() async {
    try {
      // فحص الاتصال عدة مرات للتأكد من الاستقرار
      final connection1 = await isConnected();
      await Future.delayed(const Duration(milliseconds: 500));
      final connection2 = await isConnected();
      await Future.delayed(const Duration(milliseconds: 500));
      final connection3 = await isConnected();
      
      return connection1 && connection2 && connection3;
    } catch (e) {
      return false;
    }
  }
  
  // الحصول على معلومات مفصلة عن الاتصال
  static Future<Map<String, dynamic>> getDetailedConnectionInfo() async {
    final isConnected = await NetworkUtils.isConnected();
    final connectionType = await NetworkUtils.getConnectionType();
    final connectionQuality = await NetworkUtils.getConnectionQuality();
    final bestConnection = await getBestConnectionType();
    final isStable = await isConnectionStable();
    
    return {
      'isConnected': isConnected,
      'connectionType': connectionType,
      'connectionQuality': connectionQuality,
      'bestConnection': bestConnection.toString(),
      'isStable': isStable,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
