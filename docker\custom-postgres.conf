# Custom PostgreSQL Configuration for MyStation Electricity Management System
# This file overrides default PostgreSQL settings for better performance

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 256MB                    # 25% of RAM for small systems
effective_cache_size = 1GB                # 75% of RAM
work_mem = 8MB                            # Per-operation memory
maintenance_work_mem = 64MB               # For maintenance operations
dynamic_shared_memory_type = posix

# WAL (Write-Ahead Logging) Settings
wal_level = logical                       # Required for Supabase Realtime
max_wal_senders = 10                      # For replication
max_replication_slots = 10                # For logical replication
wal_keep_size = 1GB                       # Keep WAL files
checkpoint_completion_target = 0.9        # Spread checkpoints
wal_buffers = 16MB                        # WAL buffer size

# Query Planner Settings
random_page_cost = 1.1                    # For SSD storage
effective_io_concurrency = 200            # For SSD storage
seq_page_cost = 1.0                       # Sequential scan cost

# Logging Settings
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000         # Log slow queries (1 second)
log_connections = on
log_disconnections = on
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_statement = 'all'                     # Log all statements (for development)
log_temp_files = 0                        # Log temp files

# Performance Settings
shared_preload_libraries = 'pg_stat_statements'
track_activity_query_size = 2048
track_functions = all
track_io_timing = on

# Autovacuum Settings
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1

# Lock Settings
deadlock_timeout = 1s
lock_timeout = 30s
statement_timeout = 300s                  # 5 minutes

# Locale Settings
datestyle = 'iso, mdy'
timezone = 'UTC'
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# Security Settings
ssl = off                                 # Disabled for local development
password_encryption = scram-sha-256

# Extensions for Supabase
shared_preload_libraries = 'pg_stat_statements,pg_cron,pgaudit,pgsodium,supautils,pg_graphql'

# Custom settings for electricity management
# These can be accessed in your application
app.settings.jwt_secret = 'super-secret-jwt-token-with-at-least-32-characters-long'
app.settings.jwt_exp = '3600'
app.settings.project_name = 'MyStation Electricity Management'
