import 'package:intl/intl.dart';

class DateUtils {
  // تنسيق التاريخ
  static String formatDate(DateTime date, {String format = 'yyyy-MM-dd'}) {
    return DateFormat(format).format(date);
  }
  
  // تنسيق التاريخ والوقت
  static String formatDateTime(DateTime date, {String format = 'yyyy-MM-dd HH:mm'}) {
    return DateFormat(format).format(date);
  }
  
  // تنسيق التاريخ باللغة العربية
  static String formatDateArabic(DateTime date) {
    return DateFormat.yMMMd('ar').format(date);
  }
  
  // تنسيق التاريخ والوقت باللغة العربية
  static String formatDateTimeArabic(DateTime date) {
    return DateFormat.yMMMd('ar').add_jm().format(date);
  }
  
  // الحصول على التاريخ الحالي
  static DateTime getCurrentDate() {
    return DateTime.now();
  }
  
  // الحصول على بداية اليوم
  static DateTime getStartOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }
  
  // الحصول على نهاية اليوم
  static DateTime getEndOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59);
  }
  
  // الحصول على بداية الشهر
  static DateTime getStartOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }
  
  // الحصول على نهاية الشهر
  static DateTime getEndOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59);
  }
  
  // الحصول على بداية السنة
  static DateTime getStartOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }
  
  // الحصول على نهاية السنة
  static DateTime getEndOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59);
  }
  
  // حساب الفرق بالأيام
  static int differenceInDays(DateTime date1, DateTime date2) {
    return date1.difference(date2).inDays;
  }
  
  // حساب الفرق بالأشهر
  static int differenceInMonths(DateTime date1, DateTime date2) {
    return ((date1.year - date2.year) * 12) + (date1.month - date2.month);
  }
  
  // حساب الفرق بالسنوات
  static int differenceInYears(DateTime date1, DateTime date2) {
    return date1.year - date2.year;
  }
  
  // إضافة أيام
  static DateTime addDays(DateTime date, int days) {
    return date.add(Duration(days: days));
  }
  
  // إضافة أشهر
  static DateTime addMonths(DateTime date, int months) {
    return DateTime(date.year, date.month + months, date.day);
  }
  
  // إضافة سنوات
  static DateTime addYears(DateTime date, int years) {
    return DateTime(date.year + years, date.month, date.day);
  }
  
  // التحقق من أن التاريخ في الماضي
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }
  
  // التحقق من أن التاريخ في المستقبل
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }
  
  // التحقق من أن التاريخ اليوم
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }
  
  // الحصول على اسم اليوم
  static String getDayName(DateTime date, {String locale = 'ar'}) {
    return DateFormat.EEEE(locale).format(date);
  }
  
  // الحصول على اسم الشهر
  static String getMonthName(DateTime date, {String locale = 'ar'}) {
    return DateFormat.MMMM(locale).format(date);
  }
}