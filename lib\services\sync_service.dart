// lib/services/sync_service.dart
import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:sqflite/sqflite.dart';
import 'local_database_service.dart';
import 'supabase_service.dart';

class SyncService {
  final LocalDatabaseService _localDb = LocalDatabaseService();
  Timer? _timer;
  final int _syncIntervalMinutes = 5;
  final int _maxRetries = 5;
  
  final StreamController<SyncStatus> _syncStatusController = 
      StreamController<SyncStatus>.broadcast();
  
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  Future<bool> isOnline() async {
    final List<ConnectivityResult> connectivityResults = await Connectivity().checkConnectivity();
    return connectivityResults.isNotEmpty && connectivityResults.first != ConnectivityResult.none;
  }

  void startPeriodicSync() {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(minutes: _syncIntervalMinutes), (timer) async {
      await syncData();
    });
    debugPrint('🔄 بدأت المزامنة الدورية كل $_syncIntervalMinutes دقائق');
  }

  void stopPeriodicSync() {
    _timer?.cancel();
    debugPrint('⏹️ توقفت المزامنة الدورية');
  }

  Future<void> syncData() async {
    if (!await isOnline()) {
      _syncStatusController.add(SyncStatus(
        isOnline: false,
        message: 'لا يوجد اتصال بالإنترنت',
        timestamp: DateTime.now(),
      ));
      return;
    }

    _syncStatusController.add(SyncStatus(
      isOnline: true,
      message: 'بدء المزامنة...',
      timestamp: DateTime.now(),
    ));

    try {
      final startTime = DateTime.now();
      
      await _syncUsers();
      await _syncStations();
      await _syncRoles();
      await _syncSubscriptionRequests();
      await _syncStationUsers();
      
      final duration = DateTime.now().difference(startTime);
      
      _syncStatusController.add(SyncStatus(
        isOnline: true,
        message: 'تمت المزامنة بنجاح',
        timestamp: DateTime.now(),
        duration: duration,
        isSuccess: true,
      ));
      
      debugPrint('✅ تمت المزامنة بنجاح في ${duration.inMilliseconds}ms');
    } catch (e) {
      _syncStatusController.add(SyncStatus(
        isOnline: true,
        message: 'خطأ في المزامنة: $e',
        timestamp: DateTime.now(),
        isSuccess: false,
        error: e.toString(),
      ));
      debugPrint('❌ خطأ في المزامنة: $e');
    }
  }

  Future<void> _syncUsers() async {
    try {
      final users = await SupabaseService.getUsers();
      final db = await _localDb.database;

      await db.delete('users');
      for (final user in users) {
        await db.insert('users', user, conflictAlgorithm: ConflictAlgorithm.replace);
      }
      
      debugPrint('✅ تمت مزامنة ${users.length} مستخدم');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة المستخدمين: $e');
    }
  }

  Future<void> _syncStations() async {
    try {
      final stations = await SupabaseService.getStations();
      final db = await _localDb.database;

      await db.delete('stations');
      for (final station in stations) {
        await db.insert('stations', station, conflictAlgorithm: ConflictAlgorithm.replace);
      }
      
      debugPrint('✅ تمت مزامنة ${stations.length} محطة');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة المحطات: $e');
    }
  }

  Future<void> _syncRoles() async {
    try {
      final roles = await SupabaseService.getRoles();
      final db = await _localDb.database;

      await db.delete('roles');
      for (final role in roles) {
        await db.insert('roles', role, conflictAlgorithm: ConflictAlgorithm.replace);
      }
      
      debugPrint('✅ تمت مزامنة ${roles.length} دور');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة الأدوار: $e');
    }
  }

  Future<void> _syncSubscriptionRequests() async {
    final db = await _localDb.database;
    final requests = await db.query(
      'station_subscription_requests',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );

    for (final request in requests) {
      try {
        await SupabaseService.createSubscriptionRequest(request);
        await db.update(
          'station_subscription_requests', 
          {'sync_status': 'synced'},
          where: 'id = ?', 
          whereArgs: [request['id']]
        );
        
        debugPrint('✅ تمت مزامنة طلب الاشتراك');
      } catch (e) {
        debugPrint('❌ خطأ في مزامنة طلب الاشتراك: $e');
      }
    }
  }

  Future<void> _syncStationUsers() async {
    final db = await _localDb.database;
    final stationUsers = await db.query(
      'station_users',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );

    for (final stationUser in stationUsers) {
      try {
        await SupabaseService.assignUserToStation(
          userId: stationUser['user_id'] as String,
          stationId: stationUser['station_id'] as int,
          roleId: stationUser['role_id'] as int,
        );
        
        await db.update(
          'station_users', 
          {'sync_status': 'synced'},
          where: 'id = ?', 
          whereArgs: [stationUser['id']]
        );
        
        debugPrint('✅ تمت مزامنة مستخدم المحطة');
      } catch (e) {
        debugPrint('❌ خطأ في مزامنة مستخدم المحطة: $e');
      }
    }
  }

  Future<void> initialize() async {
    debugPrint('🚀 تمت تهيئة خدمة المزامنة');
  }

  void dispose() {
    _timer?.cancel();
    _syncStatusController.close();
  }
}

class SyncStatus {
  final bool isOnline;
  final String message;
  final DateTime timestamp;
  final Duration? duration;
  final bool? isSuccess;
  final String? error;

  SyncStatus({
    required this.isOnline,
    required this.message,
    required this.timestamp,
    this.duration,
    this.isSuccess,
    this.error,
  });
}
