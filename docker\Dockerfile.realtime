# Supabase Realtime Dockerfile for MyStation
# Based on Supabase Realtime service

FROM public.ecr.aws/supabase/realtime:v2.41.23

# Set environment variables
ENV PORT=4000
ENV DB_AFTER_CONNECT_QUERY='SET search_path TO _realtime'
ENV DB_ENC_KEY=supabaserealtime
ENV ERL_AFLAGS='-proto_dist inet_tcp'
ENV ENABLE_TAILSCALE=false
ENV DNS_NODES="''"
ENV MAX_CONNECTIONS=1000
ENV REPLICATION_MODE=RLS
ENV SECURE_CHANNELS=true

# Create necessary directories (if needed)
# USER root
# RUN mkdir -p /app/config
# USER realtime

# Copy custom configuration if needed
# COPY realtime-config.json /app/config/

# Expose Realtime port
EXPOSE 4000

# Custom healthcheck
HEALTHCHECK --interval=10s --timeout=5s --retries=5 --start-period=30s \
    CMD curl -f http://localhost:4000/api/health || exit 1

# Default command
CMD ["realtime"]
