# PowerShell script to build and run MyStation with local Docker images
# Run this script to build all services locally and start them

Write-Host "🏗️ Building MyStation Electricity Management System with Local Images..." -ForegroundColor Green

# Stop any existing Supabase CLI services
Write-Host "📦 Stopping existing Supabase services..." -ForegroundColor Yellow
supabase stop

# Build and start services with local images
Write-Host "🔨 Building and starting local services..." -ForegroundColor Yellow
docker-compose -f docker-compose.override.yml up --build -d

# Wait a moment for services to start
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check service status
Write-Host "📊 Checking service status..." -ForegroundColor Yellow
docker-compose -f docker-compose.override.yml ps

# Show connection information
Write-Host ""
Write-Host "🎉 MyStation Local Build Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Connection Information:" -ForegroundColor Cyan
Write-Host "   🌐 API Gateway: http://localhost:54321" -ForegroundColor White
Write-Host "   🎛️ Studio Dashboard: http://localhost:54323" -ForegroundColor White
Write-Host "   🗄️ Database: postgresql://postgres:postgres@localhost:54322/postgres" -ForegroundColor White
Write-Host "   🔐 Auth Service: http://localhost:9999" -ForegroundColor White
Write-Host "   📁 Storage Service: http://localhost:54325" -ForegroundColor White
Write-Host "   ⚡ Realtime Service: http://localhost:4000" -ForegroundColor White
Write-Host ""
Write-Host "🔑 API Keys:" -ForegroundColor Cyan
Write-Host "   Anonymous: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" -ForegroundColor White
Write-Host "   Service Role: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU" -ForegroundColor White
Write-Host ""
Write-Host "📝 To stop services: docker-compose -f docker-compose.override.yml down" -ForegroundColor Yellow
Write-Host "📝 To view logs: docker-compose -f docker-compose.override.yml logs -f" -ForegroundColor Yellow
Write-Host "📝 To rebuild: docker-compose -f docker-compose.override.yml up --build -d" -ForegroundColor Yellow
