import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/constants/colors.dart';
import 'core/constants/strings.dart';
import 'core/constants/roles.dart';
import 'providers/auth_proivder.dart';
import 'screens/welcome_screen.dart';

final showWelcomeScreenProvider = StateProvider<bool>((ref) => true);

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final showWelcome = ref.watch(showWelcomeScreenProvider);
    final authState = ref.watch(authProvider);
    
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: AppStrings.appName,
      theme: _buildTheme(),
      home: showWelcome
          ? WelcomeScreen(
              onStart: () => ref.read(showWelcomeScreenProvider.notifier).state = false,
            )
          : _getInitialScreen(authState),
      routes: _buildRoutes(),
      onGenerateRoute: _onGenerateRoute,
    );
  }

  // بناء Theme التطبيق
  ThemeData _buildTheme() {
    return ThemeData(
      primarySwatch: Colors.blue,
      primaryColor: AppColors.primary,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      useMaterial3: true,
      
      // AppBar Theme
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 2,
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      
      // Card Theme
      cardTheme: const CardThemeData(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        margin: EdgeInsets.all(8),
      ),
    );
  }

  // بناء Routes الأساسية
  Map<String, WidgetBuilder> _buildRoutes() {
    return {
      '/': (context) => _buildLoadingScreen(), // سيتم التعامل مع هذا في _getInitialScreen
      '/login': (context) => _buildLoginScreen(),
      '/owner': (context) => _buildPlaceholderScreen(AppStrings.roleOwner),
      '/manager': (context) => _buildPlaceholderScreen(AppStrings.roleManager),
      '/collector': (context) => _buildPlaceholderScreen(AppStrings.roleCollector),
      '/financial': (context) => _buildPlaceholderScreen(AppStrings.roleFinancial),
      '/admin': (context) => _buildPlaceholderScreen(AppStrings.roleAdmin),
      '/auditor': (context) => _buildPlaceholderScreen(AppStrings.roleAuditor),
    };
  }

  // Route Generator للتنقل الديناميكي
  Route<dynamic>? _onGenerateRoute(RouteSettings settings) {
    // يمكن إضافة منطق إضافي للتنقل هنا
    return null;
  }

  // تحديد الشاشة الأولية
  Widget _getInitialScreen(AuthState authState) {
    if (authState is AuthInitial) {
      return _buildLoadingScreen();
    } else if (authState is AuthLoading) {
      return _buildLoadingScreen();
    } else if (authState is AuthAuthenticated) {
      return _getDashboardByRole(authState.role);
    } else if (authState is AuthUnauthenticated) {
      return _buildLoginScreen();
    } else if (authState is AuthError) {
      return _buildErrorScreen(authState.message);
    } else {
      return _buildLoadingScreen();
    }
  }

  // الحصول على Dashboard حسب الدور
  Widget _getDashboardByRole(int? roleId) {
    if (roleId == null) {
      return _buildUnknownRoleScreen();
    }

    switch (roleId) {
      case AppRoles.owner:
        return _buildPlaceholderScreen(AppStrings.roleOwner);
      case AppRoles.manager:
        return _buildPlaceholderScreen(AppStrings.roleManager);
      case AppRoles.collector:
        return _buildPlaceholderScreen(AppStrings.roleCollector);
      case AppRoles.financial:
        return _buildPlaceholderScreen(AppStrings.roleFinancial);
      case AppRoles.admin:
        return _buildPlaceholderScreen(AppStrings.roleAdmin);
      case AppRoles.auditor:
        return _buildPlaceholderScreen(AppStrings.roleAuditor);
      default:
        return _buildUnknownRoleScreen();
    }
  }

  // شاشة التحميل
  Widget _buildLoadingScreen() {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 24),
            Text(
              'جاري التحميل...',
              style: TextStyle(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }

  // شاشة تسجيل الدخول
  Widget _buildLoginScreen() {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.login),
        automaticallyImplyLeading: false,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.electric_bolt,
              size: 80,
              color: AppColors.primary,
            ),
            const SizedBox(height: 24),
            Text(
              'مرحباً بك في ${AppStrings.appName}',
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppStrings.appDescription,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 48),
            const Text(
              'يرجى تسجيل الدخول للمتابعة',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            // هنا يمكن إضافة نموذج تسجيل الدخول
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.divider),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  TextField(
                    decoration: InputDecoration(
                      labelText: AppStrings.email,
                      prefixIcon: const Icon(Icons.email),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: AppStrings.password,
                      prefixIcon: const Icon(Icons.lock),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // سيتم إضافة منطق تسجيل الدخول هنا
                      },
                      child: Text(AppStrings.login),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // شاشة الدور غير المعروف
  Widget _buildUnknownRoleScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('دور غير معروف'),
        automaticallyImplyLeading: false,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning_amber,
              size: 80,
              color: AppColors.warning,
            ),
            const SizedBox(height: 24),
            const Text(
              'دور المستخدم غير معروف',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'يرجى التواصل مع المسؤول لتحديد دورك في النظام',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                // سيتم إضافة منطق تسجيل الخروج هنا
              },
              child: Text(AppStrings.logout),
            ),
          ],
        ),
      ),
    );
  }

  // شاشة الخطأ
  Widget _buildErrorScreen(String message) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.error),
        backgroundColor: AppColors.error,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 24),
            Text(
              AppStrings.error,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                // يمكن إضافة منطق إعادة المحاولة هنا
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  // شاشة مؤقتة
  Widget _buildPlaceholderScreen(String title) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.dashboard,
              size: 80,
              color: AppColors.primary,
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'سيتم تطوير هذه الشاشة قريباً',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}