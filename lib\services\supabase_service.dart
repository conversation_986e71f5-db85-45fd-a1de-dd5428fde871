import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

class SupabaseService {
  static final SupabaseClient client = Supabase.instance.client;
  
  // Test connection
  static Future<bool> testConnection() async {
    try {
      await client.from('users').select().limit(1);
      debugPrint('✅ Supabase connection successful');
      return true;
    } catch (e) {
      debugPrint('❌ Supabase connection failed: $e');
      return false;
    }
  }
  
  // Users
  static Future<List<Map<String, dynamic>>> getUsers() async {
    final response = await client.from('users').select();
    return List<Map<String, dynamic>>.from(response);
  }
  
  static Future<Map<String, dynamic>?> createUser(Map<String, dynamic> userData) async {
    try {
      final response = await client.from('users').insert(userData).select().single();
      return response;
    } catch (e) {
      debugPrint('Error creating user: $e');
      return null;
    }
  }
  
  // Stations
  static Future<List<Map<String, dynamic>>> getStations() async {
    final response = await client.from('stations').select();
    return List<Map<String, dynamic>>.from(response);
  }
  
  static Future<Map<String, dynamic>?> createStation(Map<String, dynamic> stationData) async {
    try {
      final response = await client.from('stations').insert(stationData).select().single();
      return response;
    } catch (e) {
      debugPrint('Error creating station: $e');
      return null;
    }
  }
  
  // Roles
  static Future<List<Map<String, dynamic>>> getRoles() async {
    final response = await client.from('roles').select();
    return List<Map<String, dynamic>>.from(response);
  }
  
  // Station Users
  static Future<bool> assignUserToStation({
    required String userId,
    required int stationId,
    required int roleId,
  }) async {
    try {
      await client.from('station_users').insert({
        'user_id': userId,
        'station_id': stationId,
        'role_id': roleId,
      });
      return true;
    } catch (e) {
      debugPrint('Error assigning user to station: $e');
      return false;
    }
  }
  
  // Subscription Requests
  static Future<bool> createSubscriptionRequest(Map<String, dynamic> requestData) async {
    try {
      await client.from('station_subscription_requests').insert(requestData);
      return true;
    } catch (e) {
      debugPrint('Error creating subscription request: $e');
      return false;
    }
  }
}
