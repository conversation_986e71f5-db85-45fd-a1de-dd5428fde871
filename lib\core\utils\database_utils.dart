// lib/core/utils/database_utils.dart
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseUtils {
  /// Get the path to the local SQLite database
  static Future<String> getDatabasePath() async {
    final databasesPath = await getDatabasesPath();
    return join(databasesPath, 'mystation.db');
  }

  /// Check if the database file exists and is valid
  static Future<bool> isDatabaseValid() async {
    try {
      final path = await getDatabasePath();
      final database = await openDatabase(path, readOnly: true);
      await database.close();
      return true;
    } catch (e) {
      debugPrint('❌ Database validation failed: $e');
      return false;
    }
  }

  /// Get database file size
  static Future<int?> getDatabaseSize() async {
    try {
      // This is a simplified approach - in a real app you might want to use dart:io
      // For now, return null as we don't have a proper file size implementation
      return null;
    } catch (e) {
      debugPrint('❌ Could not get database size: $e');
      return null;
    }
  }

  /// Delete the local database file
  static Future<bool> removeDatabase() async {
    try {
      final path = await getDatabasePath();
      await deleteDatabase(path);
      debugPrint('🗑️ Database deleted successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Failed to delete database: $e');
      return false;
    }
  }

  /// Get database information
  static Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final path = await getDatabasePath();
      final isValid = await isDatabaseValid();
      
      return {
        'path': path,
        'isValid': isValid,
        'exists': true, // Placeholder
        'size': await getDatabaseSize(),
        'lastModified': DateTime.now().toIso8601String(), // Placeholder
      };
    } catch (e) {
      debugPrint('❌ Error getting database info: $e');
      return {
        'error': e.toString(),
        'isValid': false,
      };
    }
  }

  /// Reset database (delete and recreate)
  static Future<bool> resetDatabase() async {
    try {
      debugPrint('🔄 Resetting database...');
      
      // Delete the existing database
      final deleted = await removeDatabase();
      if (!deleted) {
        debugPrint('❌ Failed to delete existing database');
        return false;
      }
      
      debugPrint('✅ Database reset completed successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error resetting database: $e');
      return false;
    }
  }

  /// Test database connection
  static Future<bool> testDatabaseConnection() async {
    try {
      final path = await getDatabasePath();
      debugPrint('🔍 Testing database connection to: $path');
      
      final database = await openDatabase(
        path,
        version: 1,
        onCreate: (db, version) async {
          debugPrint('✅ Database created successfully');
        },
      );
      
      await database.close();
      debugPrint('✅ Database connection test successful');
      return true;
    } catch (e) {
      debugPrint('❌ Database connection test failed: $e');
      return false;
    }
  }

  /// Get database statistics
  static Future<Map<String, dynamic>> getDatabaseStats() async {
    try {
      final path = await getDatabasePath();
      final database = await openDatabase(path, readOnly: true);
      
      // Get table counts
      final tables = ['users', 'stations', 'subscription_plans', 'subscription_requests', 'station_users', 'roles', 'sync_queue'];
      final stats = <String, int>{};
      
      for (final table in tables) {
        try {
          final result = await database.rawQuery('SELECT COUNT(*) as count FROM $table');
          stats[table] = result.first['count'] as int? ?? 0;
        } catch (e) {
          stats[table] = -1; // Table doesn't exist or error
        }
      }
      
      await database.close();
      
      return {
        'path': path,
        'tables': stats,
        'totalRecords': stats.values.where((count) => count > 0).fold(0, (sum, count) => sum + count),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ Error getting database stats: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
