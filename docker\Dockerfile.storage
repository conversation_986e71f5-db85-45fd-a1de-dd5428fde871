# Supabase Storage Dockerfile for MyStation
# Based on Supabase Storage API service

FROM public.ecr.aws/supabase/storage-api:v1.26.3

# Set environment variables
ENV STORAGE_BACKEND=file
ENV FILE_STORAGE_BACKEND_PATH=/var/lib/storage
ENV FILE_SIZE_LIMIT=104857600
ENV TENANT_ID=mystation

# Create necessary directories (if needed)
# USER root
# RUN mkdir -p /var/lib/storage \
#     && mkdir -p /app/config \
#     && chown -R storage:storage /var/lib/storage
# USER storage

# Copy custom configuration if needed
# COPY storage-config.json /app/config/

# Expose Storage port
EXPOSE 5000

# Custom healthcheck
HEALTHCHECK --interval=10s --timeout=5s --retries=5 --start-period=30s \
    CMD curl -f http://localhost:5000/status || exit 1

# Default command
CMD ["storage"]
