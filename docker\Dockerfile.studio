# Supabase Studio Dockerfile for MyStation
# Based on Supabase Studio dashboard

FROM public.ecr.aws/supabase/studio:2025.08.04-sha-6e99ca6

# Set environment variables
ENV NEXT_PUBLIC_ENABLE_LOGS=true
ENV STUDIO_PORT=3000

# Create necessary directories (if needed)
# USER root
# RUN mkdir -p /app/config
# USER studio

# Copy custom configuration if needed
# COPY studio-config.json /app/config/

# Expose Studio port
EXPOSE 3000

# Custom healthcheck
HEALTHCHECK --interval=10s --timeout=5s --retries=5 --start-period=30s \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Default command
CMD ["studio"]
