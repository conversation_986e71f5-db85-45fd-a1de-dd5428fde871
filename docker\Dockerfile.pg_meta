# PostgreSQL Meta API Dockerfile for MyStation
# Based on Supabase PostgreSQL Meta service

FROM public.ecr.aws/supabase/postgres-meta:v0.91.5

# Set environment variables
ENV PG_META_PORT=8080

# Create necessary directories (if needed)
# USER root
# RUN mkdir -p /app/config
# USER pg_meta

# Copy custom configuration if needed
# COPY pg-meta-config.json /app/config/

# Expose PostgreSQL Meta port
EXPOSE 8080

# Custom healthcheck
HEALTHCHECK --interval=10s --timeout=5s --retries=5 --start-period=30s \
    CMD curl -f http://localhost:8080/health || exit 1

# Default command
CMD ["pg-meta"]
