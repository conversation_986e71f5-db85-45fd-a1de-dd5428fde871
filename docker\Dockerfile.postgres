# PostgreSQL Dockerfile for MyStation Electricity Management System
# Based on Supabase PostgreSQL with custom configurations

FROM public.ecr.aws/supabase/postgres:**********

# Set environment variables
ENV POSTGRES_USER=postgres
ENV POSTGRES_PASSWORD=postgres
ENV POSTGRES_DB=postgres
ENV POSTGRES_PORT=5432

# Install additional extensions if needed
USER root

# Copy custom configuration files
COPY custom-postgres.conf /etc/postgresql/postgresql.conf
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Create custom directories
RUN mkdir -p /var/lib/postgresql/data \
    && mkdir -p /etc/postgresql-custom \
    && chown -R postgres:postgres /var/lib/postgresql/data \
    && chown -R postgres:postgres /etc/postgresql-custom

# Switch back to postgres user
USER postgres

# Expose PostgreSQL port
EXPOSE 5432

# Custom healthcheck
HEALTHCHECK --interval=10s --timeout=5s --retries=5 --start-period=30s \
    CMD pg_isready -U postgres -h localhost -p 5432

# Set the default command
CMD ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]
