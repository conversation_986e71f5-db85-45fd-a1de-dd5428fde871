# Docker Compose Override for Supabase MyStation
# This file allows you to customize the Docker containers without modifying Supabase's internal setup

version: '3.8'

services:
  # Database container customizations
  supabase_db_mystation:
    deploy:
      resources:
        limits:
          memory: 1g
          cpus: '1.0'
        reservations:
          memory: 512m
          cpus: '0.5'
    environment:
      # Custom PostgreSQL settings
      - POSTGRES_SHARED_BUFFERS=256MB
      - POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
      - POSTGRES_WORK_MEM=8MB
      - POSTGRES_MAINTENANCE_WORK_MEM=64MB
      - POSTGRES_MAX_CONNECTIONS=200
      - POSTGRES_LOG_STATEMENT=all
      - POSTGRES_LOG_MIN_DURATION_STATEMENT=1000
    volumes:
      # Add custom PostgreSQL configuration
      - ./custom-postgres.conf:/etc/postgresql/postgresql.conf:ro
      - ./custom-init-scripts:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -h localhost -p 5432"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Kong API Gateway customizations
  supabase_kong_mystation:
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'
        reservations:
          memory: 256m
          cpus: '0.2'
    environment:
      # Custom Kong settings
      - KONG_WORKER_PROCESSES=2
      - KONG_WORKER_CONNECTIONS=1024
      - KONG_LOG_LEVEL=info
    volumes:
      # Add custom Kong configuration
      - ./custom-kong.yml:/var/lib/kong/kong.yml:ro

  # Auth service customizations
  supabase_auth_mystation:
    deploy:
      resources:
        limits:
          memory: 256m
          cpus: '0.3'
        reservations:
          memory: 128m
          cpus: '0.1'
    environment:
      # Custom Auth settings
      - GOTRUE_LOG_LEVEL=info
      - GOTRUE_RATE_LIMIT_HEADER=X-Real-IP
      - GOTRUE_RATE_LIMIT_EMAIL_SENT=60
    restart: unless-stopped

  # Storage service customizations
  supabase_storage_mystation:
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'
        reservations:
          memory: 256m
          cpus: '0.2'
    environment:
      # Custom Storage settings
      - FILE_SIZE_LIMIT=104857600  # 100MB
      - STORAGE_BACKEND=file
      - TENANT_ID=mystation
    volumes:
      # Custom storage location
      - ./storage-data:/var/lib/storage:rw
    restart: unless-stopped

  # Realtime service customizations
  supabase_realtime_mystation:
    deploy:
      resources:
        limits:
          memory: 256m
          cpus: '0.3'
        reservations:
          memory: 128m
          cpus: '0.1'
    environment:
      # Custom Realtime settings
      - MAX_CONNECTIONS=1000
      - REPLICATION_MODE=RLS
      - SECURE_CHANNELS=true
    restart: unless-stopped

  # Studio customizations
  supabase_studio_mystation:
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'
        reservations:
          memory: 256m
          cpus: '0.2'
    environment:
      # Custom Studio settings
      - STUDIO_PG_META_URL=http://supabase_pg_meta_mystation:8080
      - NEXT_PUBLIC_ENABLE_LOGS=true
    restart: unless-stopped

# Custom networks (if needed)
networks:
  supabase_network_mystation:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

# Custom volumes (if needed)
volumes:
  supabase_db_mystation:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres
  
  storage_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/storage
