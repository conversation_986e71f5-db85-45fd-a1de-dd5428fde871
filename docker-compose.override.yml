# Docker Compose for Local Builds - MyStation Electricity Management System
# This replaces Supabase CLI with locally built images

version: '3.8'

services:
  # PostgreSQL Database - Local Build
  db:
    build:
      context: ./docker
      dockerfile: Dockerfile.postgres
    image: mystation_postgres:local
    container_name: mystation_db_local
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
      POSTGRES_PORT: 5432
      JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      JWT_EXP: 3600
    ports:
      - "54322:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d:ro
    networks:
      - mystation_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -h localhost -p 5432"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1g
          cpus: '1.0'
        reservations:
          memory: 512m
          cpus: '0.5'

  # Kong API Gateway - Local Build
  kong:
    build:
      context: ./docker
      dockerfile: Dockerfile.kong
    image: mystation_kong:local
    container_name: mystation_kong_local
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl
      KONG_WORKER_PROCESSES: 2
      KONG_WORKER_CONNECTIONS: 1024
      KONG_LOG_LEVEL: info
    ports:
      - "54321:8000"
      - "54326:8001"
    volumes:
      - ./docker/kong.yml:/var/lib/kong/kong.yml:ro
    depends_on:
      - auth
      - rest
      - realtime
      - storage
    networks:
      - mystation_network
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'
        reservations:
          memory: 256m
          cpus: '0.2'

  # Supabase Auth (GoTrue) - Local Build
  auth:
    build:
      context: ./docker
      dockerfile: Dockerfile.auth
    image: mystation_auth:local
    container_name: mystation_auth_local
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: ************************************/postgres?search_path=auth
      GOTRUE_SITE_URL: http://localhost:54321
      GOTRUE_URI_ALLOW_LIST: "*"
      GOTRUE_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      GOTRUE_JWT_EXP: 3600
      GOTRUE_LOG_LEVEL: info
      GOTRUE_RATE_LIMIT_HEADER: X-Real-IP
      GOTRUE_RATE_LIMIT_EMAIL_SENT: 60
      API_EXTERNAL_URL: http://localhost:54321
      GOTRUE_DISABLE_SIGNUP: false
      GOTRUE_MAILER_AUTOCONFIRM: true
    ports:
      - "9999:9999"
    depends_on:
      - db
    networks:
      - mystation_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256m
          cpus: '0.3'
        reservations:
          memory: 128m
          cpus: '0.1'

  # PostgREST API - Local Build
  rest:
    build:
      context: ./docker
      dockerfile: Dockerfile.rest
    image: mystation_rest:local
    container_name: mystation_rest_local
    environment:
      PGRST_DB_URI: ************************************/postgres
      PGRST_DB_SCHEMAS: public,storage,graphql_public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_DB_USE_LEGACY_GUCS: "false"
    ports:
      - "3000:3000"
    depends_on:
      - db
    networks:
      - mystation_network

  # Supabase Storage - Local Build
  storage:
    build:
      context: ./docker
      dockerfile: Dockerfile.storage
    image: mystation_storage:local
    container_name: mystation_storage_local
    environment:
      ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      POSTGREST_URL: http://rest:3000
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      DATABASE_URL: ************************************/postgres
      FILE_SIZE_LIMIT: 104857600
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: mystation
    ports:
      - "54325:5000"
    volumes:
      - storage_data:/var/lib/storage
    depends_on:
      - db
      - rest
    networks:
      - mystation_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'
        reservations:
          memory: 256m
          cpus: '0.2'

  # Supabase Realtime - Local Build
  realtime:
    build:
      context: ./docker
      dockerfile: Dockerfile.realtime
    image: mystation_realtime:local
    container_name: mystation_realtime_local
    environment:
      PORT: 4000
      DB_HOST: db
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: postgres
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      SECRET_KEY_BASE: UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq
      MAX_CONNECTIONS: 1000
      REPLICATION_MODE: RLS
      SECURE_CHANNELS: true
    ports:
      - "4000:4000"
    depends_on:
      - db
    networks:
      - mystation_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256m
          cpus: '0.3'
        reservations:
          memory: 128m
          cpus: '0.1'

  # Supabase Studio - Local Build
  studio:
    build:
      context: ./docker
      dockerfile: Dockerfile.studio
    image: mystation_studio:local
    container_name: mystation_studio_local
    environment:
      STUDIO_PG_META_URL: http://pg_meta:8080
      NEXT_PUBLIC_ENABLE_LOGS: true
      SUPABASE_URL: http://kong:8000
      SUPABASE_REST_URL: http://rest:3000
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
    ports:
      - "54323:3000"
    depends_on:
      - db
      - kong
    networks:
      - mystation_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512m
          cpus: '0.5'
        reservations:
          memory: 256m
          cpus: '0.2'

  # PostgreSQL Meta API - Local Build
  pg_meta:
    build:
      context: ./docker
      dockerfile: Dockerfile.pg_meta
    image: mystation_pg_meta:local
    container_name: mystation_pg_meta_local
    environment:
      PG_META_PORT: 8080
      PG_META_DB_HOST: db
      PG_META_DB_PORT: 5432
      PG_META_DB_USER: postgres
      PG_META_DB_PASSWORD: postgres
      PG_META_DB_NAME: postgres
    ports:
      - "8080:8080"
    depends_on:
      - db
    networks:
      - mystation_network

networks:
  mystation_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

volumes:
  db_data:
    driver: local
  storage_data:
    driver: local
