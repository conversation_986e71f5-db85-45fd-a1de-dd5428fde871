version: '3.8'

services:
  # PostgreSQL Database
  db:
    build:
      context: ./docker/db
      dockerfile: Dockerfile
    image: mystation_db:local
    container_name: mystation_db_local
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
      POSTGRES_PORT: 5432
    ports:
      - "54322:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    networks:
      - mystation_network

  # Supabase Auth (GoTrue)
  auth:
    build:
      context: ./docker/auth
      dockerfile: Dockerfile
    image: mystation_auth:local
    container_name: mystation_auth_local
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: ************************************/postgres?search_path=auth
      GOTRUE_SITE_URL: http://localhost:3000
      GOTRUE_URI_ALLOW_LIST: "*"
      GOTRUE_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      GOTRUE_JWT_EXP: 3600
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
    ports:
      - "9999:9999"
    depends_on:
      - db
    networks:
      - mystation_network

  # Supabase REST API (PostgREST)
  rest:
    build:
      context: ./docker/rest
      dockerfile: Dockerfile
    image: mystation_rest:local
    container_name: mystation_rest_local
    environment:
      PGRST_DB_URI: ************************************/postgres
      PGRST_DB_SCHEMAS: public,storage,graphql_public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_DB_USE_LEGACY_GUCS: "false"
    ports:
      - "3000:3000"
    depends_on:
      - db
    networks:
      - mystation_network

  # Supabase Realtime
  realtime:
    build:
      context: ./docker/realtime
      dockerfile: Dockerfile
    image: mystation_realtime:local
    container_name: mystation_realtime_local
    environment:
      PORT: 4000
      DB_HOST: db
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: postgres
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      FLY_ALLOC_ID: fly123
      FLY_APP_NAME: realtime
      SECRET_KEY_BASE: UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq
      ERL_AFLAGS: -proto_dist inet_tcp
      ENABLE_TAILSCALE: "false"
      DNS_NODES: "''"
    ports:
      - "4000:4000"
    depends_on:
      - db
    networks:
      - mystation_network

  # Supabase Storage
  storage:
    build:
      context: ./docker/storage
      dockerfile: Dockerfile
    image: mystation_storage:local
    container_name: mystation_storage_local
    environment:
      ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      POSTGREST_URL: http://rest:3000
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      DATABASE_URL: ************************************/postgres
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
    ports:
      - "5000:5000"
    volumes:
      - storage_data:/var/lib/storage
    depends_on:
      - db
      - rest
    networks:
      - mystation_network

  # Kong API Gateway
  kong:
    build:
      context: ./docker/kong
      dockerfile: Dockerfile
    image: mystation_kong:local
    container_name: mystation_kong_local
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl
    ports:
      - "54321:8000"
      - "8001:8001"
    volumes:
      - ./docker/kong/kong.yml:/var/lib/kong/kong.yml:ro
    depends_on:
      - auth
      - rest
      - realtime
      - storage
    networks:
      - mystation_network

volumes:
  db_data:
  storage_data:

networks:
  mystation_network:
    driver: bridge
